import { IPdfOverlay, IPdfOverlayRenderer } from "@/modules/pdf/types/pdf-overlay.interface";
import { ISignatureOverlay, ISignatureRenderData } from "../../types/signature/signature-overlay.interface";

/**
 * Renderizador de overlay de assinatura para PDF
 * Implementa IPdfOverlayRenderer seguindo o padrão Strategy
 * Responsabilidade única: renderizar assinaturas no PDF
 */
export class SignatureOverlayRenderer implements IPdfOverlayRenderer {
	public readonly supportedType = "signature";

	/**
	 * Carrega uma imagem SVG
	 */
	private loadImageFromSvg(svg: string): Promise<HTMLImageElement> {
		return new Promise((resolve, reject) => {
			const img = new Image();
			img.src = `data:image/svg+xml;base64,${btoa(svg)}`;
			img.onload = () => resolve(img);
			img.onerror = error => reject(error);
		});
	}

	/**
	 * Desenha a borda de prévia da assinatura
	 */
	private drawPreviewBorder(
		context: CanvasRenderingContext2D,
		x: number,
		y: number,
		width: number,
		height: number,
		scale: number,
		borderColor: string = "#ff7e5f"
	): void {
		const gradient = context.createLinearGradient(x, y, x + width, y + height);
		gradient.addColorStop(0, borderColor);
		gradient.addColorStop(1, "#feb47b");

		const radius = 8 * scale;
		context.save();
		context.setLineDash([6, 4]);
		context.strokeStyle = gradient;
		context.lineWidth = 2;
		context.beginPath();
		context.moveTo(x + radius, y);
		context.lineTo(x + width - radius, y);
		context.quadraticCurveTo(x + width, y, x + width, y + radius);
		context.lineTo(x + width, y + height - radius);
		context.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
		context.lineTo(x + radius, y + height);
		context.quadraticCurveTo(x, y + height, x, y + height - radius);
		context.lineTo(x, y + radius);
		context.quadraticCurveTo(x, y, x + radius, y);
		context.closePath();
		context.stroke();
		context.restore();
	}

	/**
	 * Desenha o texto de prévia
	 */
	private drawPreviewLabel(
		context: CanvasRenderingContext2D,
		x: number,
		y: number,
		width: number,
		scale: number,
		text: string = "PRÉVIA",
		borderColor: string = "#ff7e5f"
	): void {
		const gradient = context.createLinearGradient(x, y, x + width, y);
		gradient.addColorStop(0, borderColor);
		gradient.addColorStop(1, "#feb47b");

		const padding = 4 * scale;
		context.save();
		context.font = `${16 * scale}px sans-serif`;
		context.textBaseline = "middle";
		context.textAlign = "center";

		const textMetrics = context.measureText(text);
		const labelWidth = textMetrics.width + padding * 2;
		const labelHeight = 20 * scale;
		const labelX = x + (width - labelWidth) / 2;
		const labelY = y - labelHeight - 4 * scale;

		// Fundo do label
		context.fillStyle = "rgba(255, 255, 255, 0.9)";
		context.strokeStyle = gradient;
		context.lineWidth = 1;

		const labelRadius = 4 * scale;
		context.beginPath();
		context.moveTo(labelX + labelRadius, labelY);
		context.lineTo(labelX + labelWidth - labelRadius, labelY);
		context.quadraticCurveTo(labelX + labelWidth, labelY, labelX + labelWidth, labelY + labelRadius);
		context.lineTo(labelX + labelWidth, labelY + labelHeight - labelRadius);
		context.quadraticCurveTo(labelX + labelWidth, labelY + labelHeight, labelX + labelWidth - labelRadius, labelY + labelHeight);
		context.lineTo(labelX + labelRadius, labelY + labelHeight);
		context.quadraticCurveTo(labelX, labelY + labelHeight, labelX, labelY + labelHeight - labelRadius);
		context.lineTo(labelX, labelY + labelRadius);
		context.quadraticCurveTo(labelX, labelY, labelX + labelRadius, labelY);
		context.closePath();
		context.fill();
		context.stroke();

		// Texto
		context.fillStyle = "#333";
		context.fillText(text, labelX + labelWidth / 2, labelY + labelHeight / 2);
		context.restore();
	}

	/**
	 * Renderiza o overlay de assinatura
	 */
	public async render(context: CanvasRenderingContext2D, overlay: IPdfOverlay, scale: number, data?: unknown): Promise<void> {
		console.log("🎨 Renderizando assinatura:", overlay.id, "escala:", scale);

		const signatureOverlay = overlay as ISignatureOverlay;
		const renderData = data as ISignatureRenderData | undefined;

		const svgData = renderData?.svg || signatureOverlay.svgData;
		if (!svgData) {
			console.warn("❌ SVG data não encontrado para assinatura", overlay.id);
			return;
		}

		console.log("✅ SVG encontrado, iniciando renderização");

		try {
			const img = await this.loadImageFromSvg(svgData);

			const BASE_SIGNATURE_WIDTH = img.naturalWidth;
			const BASE_SIGNATURE_HEIGHT = img.naturalHeight;

			const scaledCenterX = overlay.x * scale;
			const scaledCenterY = overlay.y * scale;

			const width = BASE_SIGNATURE_WIDTH * overlay.scale * scale;
			const height = BASE_SIGNATURE_HEIGHT * overlay.scale * scale;

			const x = scaledCenterX - width / 2;
			const y = scaledCenterY - height / 2;

			// Desenha a assinatura com sombra
			context.save();
			context.shadowColor = "rgba(0, 0, 0, 0.2)";
			context.shadowBlur = 8;
			context.shadowOffsetX = 0;
			context.shadowOffsetY = 4;
			context.drawImage(img, x, y, width, height);
			context.restore();

			// Se é prévia, desenha a borda e label
			const isPreview = signatureOverlay.isPreview || renderData?.showPreview;
			if (isPreview) {
				console.log("🖼️ Renderizando como prévia");
				this.drawPreviewBorder(context, x, y, width, height, scale, renderData?.previewBorderColor);
				this.drawPreviewLabel(context, x, y, width, scale, renderData?.previewText);
			}

			console.log("🎉 Assinatura renderizada com sucesso!");
		} catch (error) {
			console.error("❌ Erro ao renderizar assinatura:", error);
		}
	}
}
