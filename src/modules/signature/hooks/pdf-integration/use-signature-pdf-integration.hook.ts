import { IPdfOverlayManager } from "@/modules/pdf/types/pdf-overlay.interface";
import { useCallback, useRef } from "react";
import { 
	ISignatureOverlay, 
	ISignaturePosition, 
	IUseSignaturePdfIntegrationHook 
} from "../../types/signature/signature-overlay.interface";

/**
 * Hook para integração entre assinatura e PDF
 * Responsabilidade única: gerenciar assinaturas no contexto do PDF
 * Segue o princípio da inversão de dependência
 */
export const useSignaturePdfIntegration = (
	overlayManager: IPdfOverlayManager
): IUseSignaturePdfIntegrationHook => {
	const signatureCounterRef = useRef(0);

	/**
	 * Gera um ID único para assinatura
	 */
	const generateSignatureId = useCallback((): string => {
		signatureCounterRef.current += 1;
		return `signature-${Date.now()}-${signatureCounterRef.current}`;
	}, []);

	/**
	 * Converte posição de assinatura para overlay
	 */
	const createSignatureOverlay = useCallback((
		position: ISignaturePosition,
		svgData: string,
		isPreview: boolean = false,
		id?: string
	): ISignatureOverlay => {
		return {
			id: id || generateSignatureId(),
			type: "signature",
			page: position.page,
			x: position.x,
			y: position.y,
			scale: position.scale,
			svgData,
			isPreview,
		};
	}, [generateSignatureId]);

	const addSignatureToPage = useCallback((
		position: ISignaturePosition,
		svgData: string,
		isPreview: boolean = false
	) => {
		const overlay = createSignatureOverlay(position, svgData, isPreview);
		overlayManager.addOverlay(overlay);
	}, [overlayManager, createSignatureOverlay]);

	const removeSignatureFromPage = useCallback((signatureId: string) => {
		overlayManager.removeOverlay(signatureId);
	}, [overlayManager]);

	const updateSignaturePosition = useCallback((
		signatureId: string,
		position: ISignaturePosition
	) => {
		// Para atualizar, precisamos primeiro obter a assinatura existente
		// Como não temos um método direto para isso, vamos implementar uma busca
		const allPages = Array.from({ length: 100 }, (_, i) => i); // Assumindo máximo 100 páginas
		let existingSignature: ISignatureOverlay | null = null;

		for (const page of allPages) {
			const overlays = overlayManager.getOverlaysForPage(page);
			const signature = overlays.find(o => o.id === signatureId && o.type === "signature") as ISignatureOverlay;
			if (signature) {
				existingSignature = signature;
				break;
			}
		}

		if (existingSignature) {
			const updatedSignature = createSignatureOverlay(
				position,
				existingSignature.svgData,
				existingSignature.isPreview,
				signatureId
			);
			overlayManager.addOverlay(updatedSignature);
		}
	}, [overlayManager, createSignatureOverlay]);

	const getSignaturesForPage = useCallback((page: number): ISignatureOverlay[] => {
		const overlays = overlayManager.getOverlaysForPage(page);
		return overlays.filter(overlay => overlay.type === "signature") as ISignatureOverlay[];
	}, [overlayManager]);

	const clearAllSignatures = useCallback(() => {
		// Como não temos um método para limpar todos os overlays de um tipo específico,
		// vamos implementar uma busca e remoção
		const allPages = Array.from({ length: 100 }, (_, i) => i);
		
		for (const page of allPages) {
			const signatures = getSignaturesForPage(page);
			signatures.forEach(signature => {
				overlayManager.removeOverlay(signature.id);
			});
		}
	}, [overlayManager, getSignaturesForPage]);

	return {
		addSignatureToPage,
		removeSignatureFromPage,
		updateSignaturePosition,
		getSignaturesForPage,
		clearAllSignatures,
	};
};
