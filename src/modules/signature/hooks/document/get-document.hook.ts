import { useQuery } from "@tanstack/react-query";
import { useAtomValue, useSetAtom } from "jotai";
import { useEffect, useMemo } from "react";
import { SignatureQueryKeys } from "../../query-keys";
import { DocumentManageState } from "../../services/manage/manage-state-document";
import { documentHashCode } from "../../states/document/document-hash.state";
import { signaturePositionAtom } from "../../states/signature/signature-position.state";
import { IGetDocument } from "../../types/document/get-document.type";

export const useGetDocument = () => {
	const documentToken = useAtomValue(documentHashCode);
	const setSignaturePosition = useSetAtom(signaturePositionAtom);
	const documentService = useMemo(() => new DocumentManageState(), []);

	const query = useQuery({
		queryKey: SignatureQueryKeys.documentInfo(documentToken!),
		queryFn: () => documentService.getDocument(documentToken!),
		enabled: !!documentToken,
		retry: false,
	});

	useEffect(() => {
		const doc = query.data?.data as IGetDocument | undefined;
		if (doc?.rubric && !query.data?.isDocumentSigned) {
			setSignaturePosition({
				x: doc.rubric.coordinates.x,
				y: doc.rubric.coordinates.y,
				page: doc.rubric.pageIndex,
				scale: 1,
			});
		}
	}, [query.data, setSignaturePosition]);

	return {
		isLoading: query.isLoading,
		isFetching: query.isFetching,
		documentData: query.data,
		error: query.error,
	};
};
