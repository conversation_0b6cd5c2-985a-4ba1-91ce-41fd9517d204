import PdfViewer from "@/modules/pdf/components/pdf";
import { IPdfOverlay } from "@/modules/pdf/types/pdf-overlay.interface";
import { registerRendererAtom } from "@/modules/pdf/states";
import { useAtomValue, useSetAtom } from "jotai";
import React, { useCallback, useMemo, useEffect } from "react";

import { useAutoSignatureScroll } from "../../hooks/pdf-integration/use-signature-scroll.hook";
import { SignatureOverlayRenderer } from "../../lib/pdf-integration/signature-overlay-renderer";
import { mainRequisitToSignAtom } from "../../states/signature/main-requisit-to-sign.state";
import { rubricSvgString } from "../../states/rubric/rubric-svg.state";
import { signaturePositionAtom } from "../../states/signature/signature-position.state";
import { ISignatureOverlay, ISignatureRenderData } from "../../types/signature/signature-overlay.interface";

interface PdfWithSignatureProps {
	id: string;
	buffer: ArrayBuffer;
	isModal?: boolean;
	showSignatureAsPreview?: boolean;
}

const PdfWithSignature: React.FC<PdfWithSignatureProps> = ({ id, buffer, isModal, showSignatureAsPreview = true }) => {
	const signaturePosition = useAtomValue(signaturePositionAtom);
	const rubricSvg = useAtomValue(rubricSvgString);
	const setMainRequisitToSign = useSetAtom(mainRequisitToSignAtom);
	const registerRenderer = useSetAtom(registerRendererAtom);
	useAutoSignatureScroll(1, signaturePosition, rubricSvg);

	useEffect(() => {
		const renderer = new SignatureOverlayRenderer();
		const wasRegistered = registerRenderer(renderer);
		if (wasRegistered) {
			console.log("🔧 Renderizador registrado com sucesso");
		}
	}, [registerRenderer]);

	const signatureOverlays = useMemo((): IPdfOverlay[] => {
		console.log("🔍 Debug - signaturePosition:", signaturePosition);
		console.log("🔍 Debug - rubricSvg:", rubricSvg ? "SVG presente" : "SVG ausente");
		if (!signaturePosition || !rubricSvg) {
			console.log("❌ Não criando overlay - faltam dados");
			return [];
		}

		const signatureOverlay: ISignatureOverlay = {
			id: "current-signature",
			type: "signature",
			page: signaturePosition.page,
			x: signaturePosition.x,
			y: signaturePosition.y,
			scale: signaturePosition.scale,
			svgData: rubricSvg,
			isPreview: showSignatureAsPreview,
		};

		return [signatureOverlay];
	}, [signaturePosition, rubricSvg, showSignatureAsPreview]);

	const overlayData = useMemo(
		(): Record<string, ISignatureRenderData> => ({
			signature: {
				svg: rubricSvg || "",
				showPreview: showSignatureAsPreview,
				previewText: "PRÉVIA",
				previewBorderColor: "#ff7e5f",
			},
		}),
		[rubricSvg, showSignatureAsPreview]
	);

	const handleDownloadRequested = useCallback(() => {
		setMainRequisitToSign(true);
	}, [setMainRequisitToSign]);

	return (
		<PdfViewer
			id={id}
			buffer={buffer}
			isModal={isModal}
			overlays={signatureOverlays}
			overlayData={overlayData}
			onDownloadRequested={handleDownloadRequested}
		/>
	);
};

export default PdfWithSignature;
