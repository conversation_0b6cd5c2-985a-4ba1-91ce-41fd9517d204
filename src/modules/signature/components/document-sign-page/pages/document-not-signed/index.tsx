import { useLoadPdfProxy } from "@/modules/pdf/hooks/render/pdf-load.hook";
import { loadingSigning<PERSON>tom } from "@/modules/signature/states/signature/loading.signing.state";
import { username<PERSON>tom } from "@/modules/signature/states/signature/user-name.state";
import { IGetDocument } from "@/modules/signature/types/document/get-document.type";
import { Skeleton } from "@/shared/components/ui/skeleton";
import { useAtomValue, useSetAtom } from "jotai";
import { FileText } from "lucide-react";
import { useEffect, useMemo } from "react";
import { Card } from "../../../cards";
import { ModalCreateRubric } from "../../../modals/create-rubric";
import { DocumentModal } from "../../../modals/document-modal";
import { LoadingSign } from "../../../modals/loading-sign";
import PdfWithSignature from "../../../pdf-integration/pdf-with-signature";
import { PersonForm } from "../../forms/person-form";

export const DocumentNotSigned = ({ data }: { data: IGetDocument }) => {
	const pdfProxy = useLoadPdfProxy({ id: "not-signed-page", buffer: data.documentBuffer?.data ?? new ArrayBuffer(0) });
	const isLoadingSignDocument = useAtomValue(loadingSigningAtom);

	const setUser = useSetAtom(usernameAtom);

	useEffect(() => {
		setUser(data.signatoryName);

		return () => {
			setUser(null);
		};
	}, [data.signatoryName, setUser]);

	console.log("PDF Proxy:", pdfProxy);

	return (
		<main className="flex justify-center flex-1 items-start lg:flex-row flex-col w-full max-w-full overflow-hidden">
			<>{isLoadingSignDocument && <LoadingSign />}</>

			<section id="subscriptions" className="lg:w-4/6 hidden  rounded-md justify-center items-center h-full gap-2 lg:flex flex-col p-1 lg:p-2">
				{data?.documentBuffer?.data ? (
					<PdfWithSignature id="not-signed-page" buffer={data.documentBuffer.data} showSignatureAsPreview={true} />
				) : (
					<div className="w-full h-full flex items-center justify-center bg-black">
						<Skeleton className="w-full h-full" />
					</div>
				)}
			</section>

			<section id="subscriptions-mobile" className="lg:hidden w-full h-full   flex flex-col justify-center items-center p-1 lg:p-2 rounded-lg ">
				<Card Icon={FileText} title="Documento">
					<DocumentModal data={data} />
					<p className="text-xs text-gray-500 mt-4 text-center">
						* É necessário realizar a leitura do documento e verificar os termos abaixo
					</p>
				</Card>
			</section>
			<section id="actions" className="lg:w-2/6 w-full  h-full p-1 lg:p-2">
				<PersonForm />
			</section>

			<ModalCreateRubric />
		</main>
	);
};
