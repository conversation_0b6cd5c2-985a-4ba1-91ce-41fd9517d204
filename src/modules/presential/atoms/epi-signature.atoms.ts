import { atom } from "jotai";

export interface IEpiSignatureData {
	selectedPersonId?: number;
	selectedTermId?: number;
	selectedEpiGroupId?: number;
	selectedEpis?: unknown[];
	epiItems?: unknown[];
	personCpf?: string;
	personName?: string;
	photo?: File;
	signatoryCount?: number;
	signature?: string;
}

export const currentStepAtom = atom<number>(1);
export const isLoadingAtom = atom<boolean>(false);
export const signatureDataAtom = atom<IEpiSignatureData>({});

export const canProceedAtom = atom(get => {
	const currentStep = get(currentStepAtom);
	const signatureData = get(signatureDataAtom);

	switch (currentStep) {
		case 1:
			// Validar se termo foi selecionado
			return !!signatureData.selectedTermId;
		case 2:
			// Validar se EPIs foram selecionados e quantidade definida
			return !!(signatureData.selectedEpis?.length && signatureData.signatoryCount);
		case 3:
			// Validar se CPF, nome e foto foram preenchidos
			return !!(signatureData.personCpf && signatureData.personName && signatureData.photo);
		case 4:
			// Validar se assinatura foi realizada
			return !!signatureData.signature;
		case 5:
			// Etapa de confirmação - sempre válida se chegou até aqui
			return true;
		default:
			return false;
	}
});

// Atoms derivados para controle do stepper
export const totalStepsAtom = atom<number>(5);
export const isFirstStepAtom = atom(get => get(currentStepAtom) === 1);
export const isLastStepAtom = atom(get => get(currentStepAtom) === get(totalStepsAtom));
export const progressAtom = atom(get => (get(currentStepAtom) / get(totalStepsAtom)) * 100);

// Actions atoms
export const nextStepAtom = atom(null, (get, set) => {
	const currentStep = get(currentStepAtom);
	const canProceed = get(canProceedAtom);
	const totalSteps = get(totalStepsAtom);

	if (canProceed && currentStep < totalSteps) {
		set(currentStepAtom, currentStep + 1);
	}
});

export const previousStepAtom = atom(null, (get, set) => {
	const currentStep = get(currentStepAtom);
	if (currentStep > 1) {
		set(currentStepAtom, currentStep - 1);
	}
});

export const setStepAtom = atom(null, (get, set, step: number) => {
	const totalSteps = get(totalStepsAtom);
	if (step >= 1 && step <= totalSteps) {
		set(currentStepAtom, step);
	}
});

export const updateSignatureDataAtom = atom(null, (get, set, data: Partial<IEpiSignatureData>) => {
	const currentData = get(signatureDataAtom);
	set(signatureDataAtom, { ...currentData, ...data });
});

export const resetProcessAtom = atom(null, (_get, set) => {
	set(currentStepAtom, 1);
	set(signatureDataAtom, {});
	set(isLoadingAtom, false);
});
