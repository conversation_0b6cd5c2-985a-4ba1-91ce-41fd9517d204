import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { deleteTerm } from "../../services/requests/terms/delete";

export const useDeleteTermMutation = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationKey: ["terms", "delete"],
		mutationFn: async (idTermo: string | number) => {
			const res = await deleteTerm(idTermo);
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["terms", "all"] });
			toast.dismiss();
			toast.success("Termo deletado com sucesso!");
		},
		onError: (error: Error) => {
			toast.dismiss();
			toast.error(`Erro ao deletar termo: ${error.message}`);
		},
	});
};
