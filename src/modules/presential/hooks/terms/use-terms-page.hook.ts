import { useState } from "react";
import { ITerm } from "@/modules/presential/services/requests/terms/find-all";

export function useTermsPage() {
	const [selectedTermId, setSelectedTermId] = useState<number | null>(null);
	const [isCreateTermOpen, setIsCreateTermOpen] = useState(false);
	const [isCreateTagOpen, setIsCreateTagOpen] = useState(false);
	const [isEditTermOpen, setIsEditTermOpen] = useState(false);
	const [isViewTermOpen, setIsViewTermOpen] = useState(false);
	const [editTermData, setEditTermData] = useState({ title: "", idTermTag: "" });
	const [selectedTermForView, setSelectedTermForView] = useState<ITerm | null>(null);

	const handleSelectTermForEdit = (term: ITerm) => {
		setSelectedTermId(term.id);
		setEditTermData({ title: term.fileName, idTermTag: term.idTermTag.toString() });
		setIsEditTermOpen(true);
	};

	const handleSelectTermForView = (term: ITerm) => {
		setSelectedTermForView(term);
		setIsViewTermOpen(true);
	};

	const handleCloseViewTerm = () => {
		setSelectedTermForView(null);
		setIsViewTermOpen(false);
	};

	return {
		selectedTermId,
		isCreateTermOpen,
		isCreateTagOpen,
		isEditTermOpen,
		isViewTermOpen,
		editTermData,
		selectedTermForView,
		setSelectedTermId,
		setIsCreateTermOpen,
		setIsCreateTagOpen,
		setIsEditTermOpen,
		handleSelectTermForEdit,
		handleSelectTermForView,
		handleCloseViewTerm,
	};
}
