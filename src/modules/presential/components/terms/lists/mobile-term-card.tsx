import { ITerm } from "@/modules/presential/services/requests/terms/find-all";
import { Button } from "@/shared/components/ui/button";
import { Separator } from "@/shared/components/ui/separator";
import { Skeleton } from "@/shared/components/ui/skeleton";
import { Calendar, ChevronDown, Download, Edit, Eye, FileText, Tag } from "lucide-react";
import { useState } from "react";

interface MobileTermCardProps {
	term: ITerm;
	onEdit: (term: ITerm) => void;
	onView: (term: ITerm) => void;
	getTagName: (id: number) => string | undefined;
	formatDate: (dateString: string) => string;
	isTagsLoading: boolean;
}

export const MobileTermCard: React.FC<MobileTermCardProps> = ({ term, onEdit, onView, getTagName, formatDate, isTagsLoading }) => {
	const [isExpanded, setIsExpanded] = useState(false);

	const handleToggleExpand = () => {
		setIsExpanded(prev => !prev);
	};

	const title = term.title;
	const shortFileName = title.length > 30 ? title.substring(0, 27) + "..." : title;

	return (
		<div className="bg-white rounded-lg shadow border border-gray-100 mb-3 overflow-hidden">
			<div
				className="p-3 cursor-pointer transition-colors hover:bg-gray-50"
				onClick={handleToggleExpand}
				onKeyDown={e => {
					if (e.key === "Enter" || e.key === " ") {
						e.preventDefault();
						handleToggleExpand();
					}
				}}
				tabIndex={0}
				role="button"
				aria-expanded={isExpanded}
				aria-label="Expandir para ver mais detalhes do termo"
			>
				<div className="flex items-center justify-between mb-2">
					<div className="flex items-center gap-2 flex-1 min-w-0">
						<div className="p-2 rounded-md bg-pormade/10 text-pormade flex-shrink-0">
							<FileText size={18} className="text-pormade" />
						</div>
						<h3 className="font-medium text-gray-900 truncate" title={title}>
							{shortFileName}
						</h3>
					</div>
					<ChevronDown size={18} className={`text-gray-400 transition-transform duration-300 ${isExpanded ? "rotate-180" : ""}`} />
				</div>

				<div className="flex items-center justify-between">
					<div className="flex items-center gap-1 text-xs text-gray-500">
						<Calendar size={12} className="text-gray-400" />
						<span>{formatDate(term.createDate)}</span>
					</div>

					{isTagsLoading ? (
						<Skeleton className="h-5 w-20 rounded-full" />
					) : (
						<div className="inline-flex items-center gap-1 px-2 py-1 rounded-full bg-pormade/10 text-pormade text-xs font-medium border border-pormade/20">
							<Tag size={10} className="text-pormade flex-shrink-0" />
							<span className="truncate max-w-[100px]">{getTagName(term.idTermTag)}</span>
						</div>
					)}
				</div>
			</div>
			{isExpanded && (
				<>
					<Separator />
					<div className="p-3 bg-gray-50">
						<div className="flex flex-col sm:flex-row gap-2">
							<Button
								variant="outline"
								size="sm"
								className="flex-1 h-8  p-2 border-gray-200 bg-white hover:bg-gray-50 hover:text-pormade text-gray-700"
								onClick={e => {
									e.stopPropagation();
									onView(term);
								}}
							>
								<Eye size={14} className="mr-2" /> Visualizar
							</Button>
							<Button
								variant="outline"
								size="sm"
								className="flex-1 h-8  p-2 border-gray-200 bg-white hover:bg-gray-50 hover:text-blue-600 text-gray-700"
								onClick={e => {
									e.stopPropagation();
									console.log("Baixar termo:", term.id);
								}}
							>
								<Download size={14} className="mr-2" /> Baixar
							</Button>
							<Button
								variant="outline"
								size="sm"
								className="flex-1 h-8 p-2 border-gray-200 bg-white hover:bg-gray-50 hover:text-amber-600 text-gray-700"
								onClick={e => {
									e.stopPropagation();
									onEdit(term);
								}}
							>
								<Edit size={14} className="mr-2" /> Editar
							</Button>
						</div>
					</div>
				</>
			)}
		</div>
	);
};
