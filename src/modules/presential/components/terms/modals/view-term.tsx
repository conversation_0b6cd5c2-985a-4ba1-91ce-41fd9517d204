"use client";
import { useLoadPdfProxy } from "@/modules/pdf/hooks/render/pdf-load.hook";
import { useGetTermFileQuery } from "@/modules/presential/hooks/terms/get-term-file.hook";
import { ITerm } from "@/modules/presential/services/requests/terms/find-all";
import { Modal } from "@/shared/components/custom/modal";
import { Button } from "@/shared/components/ui/button";
import { Skeleton } from "@/shared/components/ui/skeleton";
import { FileText, X } from "lucide-react";
import dynamic from "next/dynamic";

const PdfViewer = dynamic(() => import("@/modules/pdf/components/pdf"), {
	ssr: false,
	loading: () => (
		<div className="flex items-center justify-center h-96">
			<Skeleton className="w-full h-full" />
		</div>
	),
});

interface ViewTermModalProps {
	isOpen: boolean;
	onClose: () => void;
	term: ITerm | null;
}

const LoadingState = ({ message }: { message: string }) => (
	<div className="flex items-center justify-center h-full">
		<div className="text-center">
			<Skeleton className="w-16 h-16 rounded-full mx-auto mb-4" />
			<p className="text-gray-500">{message}</p>
		</div>
	</div>
);

const ErrorState = ({ title, message, description }: { title: string; message: string; description?: string }) => (
	<div className="flex items-center justify-center h-full">
		<div className="text-center max-w-md">
			<FileText className="w-16 h-16 text-red-400 mx-auto mb-4" />
			<p className="text-red-500 font-medium mb-2">{title}</p>
			<p className="text-sm text-gray-600 leading-relaxed">{message}</p>
			{description && <p className="text-xs text-gray-500 mt-3">{description}</p>}
		</div>
	</div>
);

const EmptyState = ({ message }: { message: string }) => (
	<div className="flex items-center justify-center h-full">
		<div className="text-center">
			<FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
			<p className="text-gray-500">{message}</p>
		</div>
	</div>
);

const Term = ({ id, buffer }: { id: string; buffer: ArrayBuffer }) => {
	const { isLoading: isPdfLoading, error: pdfError } = useLoadPdfProxy({
		id: "not-signed-page",
		buffer: buffer ?? new ArrayBuffer(0),
	});

	if (isPdfLoading) return <LoadingState message="Processando PDF..." />;

	if (pdfError) {
		return (
			<ErrorState
				title="Erro no documento PDF"
				message={pdfError}
				description="Verifique se o arquivo não está corrompido ou entre em contato com o suporte."
			/>
		);
	}

	return <PdfViewer id={id} buffer={buffer} isModal />;
};

export const ViewTermModal = ({ isOpen, onClose, term }: ViewTermModalProps) => {
	const { data: termFileData, isLoading, error } = useGetTermFileQuery(term?.id || 0, !!term && isOpen);

	const renderContent = () => {
		if (!term) return <EmptyState message="Termo não encontrado" />;
		if (isLoading) return <LoadingState message="Carregando termo..." />;
		if (error) return <ErrorState title="Erro ao carregar o termo" message="Não foi possível carregar o arquivo do termo" />;
		if (!termFileData) return <EmptyState message="Nenhum arquivo disponível" />;

		return (
			<div className="h-full">
				<Term id={`term-${term.id}`} buffer={termFileData} />
			</div>
		);
	};

	return (
		<Modal isOpen={isOpen} onRequestClose={onClose} shouldCloseOnOverlayClick={true} className="max-w-6xl w-[95vw] max-h-[95vh] h-[95vh]">
			<div className="flex flex-col h-full bg-white p-2 rounded-lg shadow-lg">
				<header className="flex items-center justify-between p-4 border-b border-gray-200">
					<div className="flex items-center gap-3">
						<div className="p-2 rounded-md bg-pormade/10 text-pormade">
							<FileText size={20} className="text-pormade" />
						</div>
						<div>
							<h2 className="text-lg font-semibold text-gray-900">Visualizar Termo</h2>
							<p className="text-sm text-gray-500 truncate max-w-md" title={term?.title || ""}>
								{term?.title || "Termo não encontrado"}
							</p>
						</div>
					</div>
					<Button variant="ghost" size="sm" className="hover:bg-gray-100" onClick={onClose} aria-label="Fechar modal">
						<X size={20} />
					</Button>
				</header>

				<main className="flex-1 p-4  overflow-hidden">{renderContent()}</main>
			</div>
		</Modal>
	);
};
