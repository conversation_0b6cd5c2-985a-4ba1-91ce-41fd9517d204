import { signature<PERSON><PERSON><PERSON><PERSON>, updateSignatureData<PERSON>tom } from "@/modules/presential/atoms/epi-signature.atoms";
import { useFindAllTermsQuery } from "@/modules/presential/hooks/terms/find-all-terms-query.hook";
import { ITerm } from "@/modules/presential/services/requests/terms/find-all";
import { ViewTermModal } from "@/modules/presential/components/terms/modals/view-term";
import { Card, CardContent } from "@/shared/components/ui/card";
import { Skeleton } from "@/shared/components/ui/skeleton";
import { Button } from "@/shared/components/ui/button";
import { cn } from "@/shared/lib/utils";
import { useAtomValue, useSetAtom } from "jotai";
import { Check, FileText, Eye } from "lucide-react";
import { useState } from "react";

const SectionHeader = () => (
	<div className="text-center mb-3 sm:mb-6 px-2 sm:px-4">
		<div className="inline-flex items-center justify-center w-10 h-10 sm:w-16 sm:h-16 bg-pormade/10 rounded-xl sm:rounded-2xl mb-2 sm:mb-3">
			<FileText className="w-5 h-5 sm:w-8 sm:h-8 text-pormade" />
		</div>
		<h2 className="text-base sm:text-xl font-bold text-gray-900 mb-0.5 sm:mb-1">Selecione o Termo</h2>
		<p className="text-gray-600 text-xs sm:text-base leading-relaxed max-w-sm mx-auto">Escolha o termo para assinatura de EPI</p>
	</div>
);

const LoadingSkeleton = () => (
	<div className="space-y-3 sm:space-y-6">
		<SectionHeader />
		<div className="px-2 sm:px-4">
			<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 sm:gap-4">
				{Array.from({ length: 6 }).map((_, index) => (
					<Card key={index} className="h-16 sm:h-24 p-2 sm:p-4 animate-pulse">
						<div className="flex items-center gap-2 sm:gap-4 h-full">
							<Skeleton className="h-6 w-6 sm:h-10 sm:w-10 rounded-lg flex-shrink-0" />
							<div className="flex-1 space-y-1 sm:space-y-3">
								<Skeleton className="h-2 sm:h-4 w-full" />
								<Skeleton className="h-1.5 sm:h-3 w-2/3" />
							</div>
						</div>
					</Card>
				))}
			</div>
		</div>
	</div>
);

const EmptyState = () => (
	<div className="px-2 sm:px-4">
		<Card className="text-center py-6 sm:py-12 border-dashed border-2 border-gray-200 bg-gray-50/50">
			<CardContent className="space-y-2 sm:space-y-4">
				<div className="inline-flex items-center justify-center w-10 h-10 sm:w-16 sm:h-16 bg-gray-100 rounded-xl sm:rounded-2xl mx-auto">
					<FileText className="w-5 h-5 sm:w-8 sm:h-8 text-gray-400" />
				</div>
				<div className="px-2 sm:px-4">
					<h3 className="text-sm sm:text-lg font-semibold text-gray-900 mb-1 sm:mb-2">Nenhum termo encontrado</h3>
					<p className="text-gray-500 text-xs sm:text-base max-w-xs sm:max-w-sm mx-auto leading-relaxed">
						Não há termos disponíveis para seleção no momento. Entre em contato com o administrador.
					</p>
				</div>
			</CardContent>
		</Card>
	</div>
);

interface TermCardProps {
	term: ITerm;
	isSelected: boolean;
	onSelect: (term: ITerm) => void;
}

const TermCard = ({ term, isSelected, onSelect }: TermCardProps) => {
	const [isViewModalOpen, setIsViewModalOpen] = useState(false);

	const handleViewTerm = (e: React.MouseEvent) => {
		e.stopPropagation();
		setIsViewModalOpen(true);
	};

	return (
		<>
			<Card
				className={cn(
					"cursor-pointer transition-all duration-200 hover:shadow-md active:scale-[0.98]",
					"border h-16 sm:h-24 flex items-center px-2 sm:px-4 relative overflow-hidden",
					"touch-manipulation",
					isSelected
						? "border-pormade bg-gradient-to-r from-pormade/5 to-pormade/10 shadow-sm"
						: "border-gray-200 bg-white hover:border-gray-300"
				)}
				onClick={() => onSelect(term)}
			>
				{isSelected && (
					<div className="absolute top-2 right-2 sm:top-3 sm:right-3">
						<div className="w-4 h-4 sm:w-6 sm:h-6 bg-pormade rounded-full flex items-center justify-center shadow-sm">
							<Check className="w-2 h-2 sm:w-4 sm:h-4 text-white" />
						</div>
					</div>
				)}
				<CardContent className="flex items-center gap-2 sm:gap-4 p-0 w-full pr-12 sm:pr-16">
					<div
						className={cn(
							"p-1.5 sm:p-2.5 rounded-md sm:rounded-lg flex-shrink-0 transition-colors duration-200",
							isSelected ? "bg-pormade text-white" : "bg-gray-100 text-pormade"
						)}
					>
						<FileText className="w-3.5 h-3.5 sm:w-5 sm:h-5" />
					</div>
					<div className="flex-1 min-w-0">
						<h3
							className={cn(
								"font-medium text-xs sm:text-base leading-tight mb-0",
								"line-clamp-1 sm:line-clamp-2 break-words",
								isSelected ? "text-pormade" : "text-gray-900"
							)}
						>
							{term.title || term.fileName}
						</h3>
						<p className="text-[10px] sm:text-sm text-gray-400 font-mono">#{term.id}</p>
					</div>
					<Button
						variant="ghost"
						size="sm"
						className="h-8 w-8 p-0 hover:bg-gray-100 hover:text-pormade flex-shrink-0"
						onClick={handleViewTerm}
						title="Visualizar termo"
					>
						<Eye className="w-4 h-4" />
					</Button>
				</CardContent>
			</Card>

			<ViewTermModal isOpen={isViewModalOpen} onClose={() => setIsViewModalOpen(false)} term={term} />
		</>
	);
};

interface SelectionConfirmationProps {
	selectedTerm: ITerm;
}

const SelectionConfirmation = ({ selectedTerm }: SelectionConfirmationProps) => (
	<div className="mt-3 sm:mt-6 mx-2 sm:mx-4">
		<div className="p-2 sm:p-4 bg-gradient-to-r from-pormade/15 to-pormade/10 border border-pormade rounded-lg sm:rounded-xl animate-in slide-in-from-top-2 duration-500">
			<div className="flex items-center gap-2 sm:gap-3">
				<div className="w-5 h-5 sm:w-8 sm:h-8 bg-pormade rounded-full flex items-center justify-center flex-shrink-0">
					<Check className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
				</div>
				<div className="flex-1 min-w-0">
					<p className="text-xs sm:text-base font-semibold text-pormade mb-0.5">Termo selecionado</p>
					<p className="text-xs sm:text-base text-green-700 break-words leading-snug sm:leading-relaxed line-clamp-2 sm:line-clamp-none">
						{selectedTerm.title || selectedTerm.fileName}
					</p>
				</div>
			</div>
		</div>
	</div>
);

export const TermSelectionStep = () => {
	const { data: termsData, isLoading } = useFindAllTermsQuery();
	const signatureData = useAtomValue(signatureDataAtom);
	const updateSignatureData = useSetAtom(updateSignatureDataAtom);

	const terms = termsData?.success ? termsData.data : [];
	const selectedTermId = signatureData.selectedTermId;
	const selectedTerm = terms.find(term => term.id === selectedTermId);

	const handleSelectTerm = (term: ITerm) => {
		updateSignatureData({ selectedTermId: term.id });
	};

	if (isLoading) {
		return <LoadingSkeleton />;
	}

	return (
		<div className="space-y-3 sm:space-y-6 overflow-hidden max-w-6xl mx-auto min-h-0">
			<SectionHeader />

			{terms.length === 0 ? (
				<EmptyState />
			) : (
				<>
					<div className="px-2 sm:px-0">
						<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 sm:gap-4">
							{terms.map(term => (
								<TermCard key={term.id} term={term} isSelected={selectedTermId === term.id} onSelect={handleSelectTerm} />
							))}
						</div>
					</div>

					{selectedTermId && selectedTerm && <SelectionConfirmation selectedTerm={selectedTerm} />}
				</>
			)}
		</div>
	);
};
