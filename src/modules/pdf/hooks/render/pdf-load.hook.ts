"use client";
import { use<PERSON>et<PERSON><PERSON> } from "jotai";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { PdfService } from "../../services/pdf-service";
import { pdfDocumentProxy } from "../../states/pdf-proxy.state";

interface LoadPdfProxyProps {
	id: string;
	buffer: ArrayBuffer;
	service?: PdfService;
	onLoadStart?: () => void;
	onLoadEnd?: () => void;
	onError?: (error: string) => void;
}

interface ILoadPdfProxyReturn {
	clearCache: () => void;
	isLoading: boolean;
	error: string | null;
	retry: () => void;
}

const ERRORS = {
	EMPTY: "Buffer de PDF vazio ou inválido",
	INVALID: "O arquivo não é um documento PDF válido",
	CORRUPTED: "Estrutura do PDF é inválida ou arquivo corrompido",
	PROCESS: "Não foi possível processar o documento PDF",
	UNKNOWN: "Erro desconhecido ao carregar o PDF",
} as const;

export const useLoadPdfProxy = ({ id, buffer, service, onLoadStart, onLoadEnd, onError }: LoadPdfProxyProps): ILoadPdfProxyReturn => {
	const setDocumentProxy = useSetAtom(pdfDocumentProxy);
	const pdfService = useMemo(() => service || new PdfService(), [service]);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const abortRef = useRef<AbortController | null>(null);

	const validateBuffer = useCallback((buf: ArrayBuffer) => {
		if (!buf || buf.byteLength === 0) throw new Error(ERRORS.EMPTY);
		const header = String.fromCharCode(...new Uint8Array(buf).slice(0, 4));
		if (header !== "%PDF") throw new Error(ERRORS.INVALID);
	}, []);

	const getError = useCallback((err: unknown): string => {
		if (!(err instanceof Error)) return ERRORS.UNKNOWN;
		const msg = err.message.toLowerCase();
		if (msg.includes("invalid pdf structure") || msg.includes("invalidpdfexception")) return ERRORS.CORRUPTED;
		if (msg.includes("não é um pdf válido")) return ERRORS.INVALID;
		if (msg.includes("falha ao carregar o documento pdf")) return ERRORS.PROCESS;
		return err.message || ERRORS.UNKNOWN;
	}, []);

	const loadDocument = useCallback(async () => {
		abortRef.current?.abort();
		abortRef.current = new AbortController();

		try {
			if (typeof window === "undefined") return;
			validateBuffer(buffer);
			setIsLoading(true);
			setError(null);
			onLoadStart?.();

			const pdf = await pdfService.getPdfDocumentProxy({ id, buffer });
			if (abortRef.current.signal.aborted) return;
			if (!pdf) throw new Error(ERRORS.PROCESS);

			setDocumentProxy(pdf);
		} catch (err) {
			if (abortRef.current?.signal.aborted) return;
			const msg = getError(err);
			console.error("Erro ao carregar o PDF:", err);
			setError(msg);
			setDocumentProxy(null);
			onError?.(msg);
		} finally {
			if (!abortRef.current?.signal.aborted) {
				setIsLoading(false);
				onLoadEnd?.();
			}
		}
	}, [id, buffer, pdfService, validateBuffer, getError, setDocumentProxy, onLoadStart, onLoadEnd, onError]);

	useEffect(() => {
		loadDocument();
		return () => abortRef.current?.abort();
	}, [loadDocument]);

	return {
		clearCache: () => pdfService.clearCache(),
		isLoading,
		error,
		retry: loadDocument,
	};
};
