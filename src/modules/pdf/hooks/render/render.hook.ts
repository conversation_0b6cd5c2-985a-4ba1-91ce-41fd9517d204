import { useDebounce } from "@/shared/hooks/utils/debounce";
import type { PDFDocumentProxy, PDFPageProxy } from "pdfjs-dist";
import { RefObject, useCallback, useEffect, useLayoutEffect, useRef } from "react";
import { IPdfOverlay } from "../../types/pdf-overlay.interface";
import { useVisibility } from "../visual-interactions/use-canva-visible.hook";

export interface IUsePdfRenderer {
	pdfDocument: PDFDocumentProxy;
	pageNumber: number;
	zoom: number;
	canvasRef: React.RefObject<HTMLCanvasElement | null>;
	onRenderComplete?: () => void;
	overlays?: IPdfOverlay[];
	overlayData?: Record<string, unknown>;
	forceRenderAllPages?: boolean;
	onRenderOverlays?: (context: CanvasRenderingContext2D, page: number, scale: number, overlayData?: Record<string, unknown>) => Promise<void>;
}

export const usePdfRenderer = ({
	pdfDocument,
	pageNumber,
	zoom,
	canvasRef,
	onRenderComplete,
	overlayData,
	forceRenderAllPages,
	onRenderOverlays,
}: Omit<IUsePdfRenderer, "overlays">) => {
	const renderTaskRef = useRef<ReturnType<PDFPageProxy["render"]> | null>(null);
	const isRenderingOverlaysRef = useRef(false);
	const lastRenderedPageRef = useRef<number | null>(null);
	const lastRenderedZoomRef = useRef<number | null>(null);
	const isVisible = useVisibility(canvasRef as RefObject<HTMLElement>, 0.1);

	const cancelRenderTask = () => {
		renderTaskRef.current?.cancel();
		renderTaskRef.current = null;
	};

	const renderPage = useCallback(async () => {
		if (!canvasRef.current) return;

		try {
			const page = await pdfDocument.getPage(pageNumber);
			const baseViewport = page.getViewport({ scale: 1 });
			const deviceScale = window.devicePixelRatio || 1;
			const scale = zoom * deviceScale;
			const viewport = page.getViewport({ scale });
			const context = canvasRef.current.getContext("2d");
			if (!context) return;

			canvasRef.current.width = viewport.width;
			canvasRef.current.height = viewport.height;

			if (window.matchMedia("(max-width: 768px)").matches) {
				const containerWidth = canvasRef.current.parentElement?.clientWidth || window.innerWidth;
				const aspectRatio = baseViewport.height / baseViewport.width;
				canvasRef.current.style.width = "100%";
				canvasRef.current.style.height = `${containerWidth * aspectRatio}px`;
			} else {
				canvasRef.current.style.width = `${baseViewport.width * zoom}px`;
				canvasRef.current.style.height = `${baseViewport.height * zoom}px`;
			}

			cancelRenderTask();
			const renderTask = page.render({ canvasContext: context, viewport });
			renderTaskRef.current = renderTask;
			await renderTask.promise;

			if (onRenderOverlays && !isRenderingOverlaysRef.current) {
				isRenderingOverlaysRef.current = true;
				try {
					await onRenderOverlays(context, pageNumber - 1, scale, overlayData);
				} catch (error) {
					console.error("Erro ao renderizar overlays:", error);
				} finally {
					isRenderingOverlaysRef.current = false;
				}
			}

			onRenderComplete?.();
		} catch (error: unknown) {
			if (
				typeof error === "object" &&
				error !== null &&
				("name" in error || "message" in error) &&
				((error as { name?: string }).name === "RenderingCancelledException" ||
					(typeof (error as { message?: string }).message === "string" &&
						(error as { message: string }).message.includes("Rendering cancelled")))
			) {
				return;
			}
			console.error("Erro ao renderizar a página", error);
		}
	}, [pdfDocument, pageNumber, zoom, onRenderComplete, canvasRef, onRenderOverlays, overlayData]);

	useEffect(() => {
		const renderOverlays = async () => {
			if (!onRenderOverlays || !canvasRef.current || isRenderingOverlaysRef.current) return;
			if (lastRenderedPageRef.current !== pageNumber) return;

			const context = canvasRef.current.getContext("2d");
			if (!context) return;

			const deviceScale = window.devicePixelRatio || 1;
			const scale = zoom * deviceScale;

			isRenderingOverlaysRef.current = true;
			try {
				await onRenderOverlays(context, pageNumber - 1, scale, overlayData);
			} catch (error) {
				console.error("❌ [OVERLAY DEBUG] Erro ao renderizar overlays:", error);
			} finally {
				isRenderingOverlaysRef.current = false;
			}
		};

		renderOverlays();
	}, [onRenderOverlays, overlayData, pageNumber, zoom, canvasRef]);

	useLayoutEffect(() => {
		if (forceRenderAllPages || (isVisible && canvasRef.current && canvasRef.current.clientWidth > 0 && canvasRef.current.clientHeight > 0)) {
			renderPage().then(() => {
				lastRenderedPageRef.current = pageNumber;
				lastRenderedZoomRef.current = zoom;
			});
		}
	}, [forceRenderAllPages, renderPage, canvasRef, isVisible, pageNumber, zoom]);

	const debouncedRenderPage = useDebounce(renderPage, 200, [renderPage]);
	useEffect(() => {
		window.addEventListener("resize", debouncedRenderPage);
		return () => window.removeEventListener("resize", debouncedRenderPage);
	}, [debouncedRenderPage]);
};
