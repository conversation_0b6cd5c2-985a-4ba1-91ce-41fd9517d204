import { atom } from "jotai";
import { IPdfOverlayRenderer } from "../types/pdf-overlay.interface";

export const globalRenderersAtom = atom<Map<string, IPdfOverlayRenderer>>(new Map());

export const isRendererRegisteredAtom = (type: string) => atom(get => get(globalRenderersAtom).has(type));

export const registerRendererAtom = atom(null, (get, set, renderer: IPdfOverlayRenderer) => {
	const currentRenderers = get(globalRenderersAtom);
	if (!currentRenderers.has(renderer.supportedType)) {
		const newRenderers = new Map(currentRenderers);
		newRenderers.set(renderer.supportedType, renderer);
		set(globalRenderersAtom, newRenderers);
		return true;
	}
	return false;
});
