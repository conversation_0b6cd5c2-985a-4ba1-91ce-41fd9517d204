"use client";
import type { PDFDocumentLoadingTask, PDFDocumentProxy } from "pdfjs-dist";
import { PDF_WORKER_SETTINGS } from "../data/pdf-config";

interface PdfJsModule {
	getDocument: (options: { [key: string]: unknown }) => PDFDocumentLoadingTask;
	GlobalWorkerOptions: {
		workerSrc: string;
	};
}

const loadPdfJs = async (): Promise<PdfJsModule> => {
	if (typeof window === "undefined") throw new Error("pdfjs-dist só pode ser usado no lado do cliente");
	const pdfjs = await import("pdfjs-dist");
	return {
		getDocument: pdfjs.getDocument,
		GlobalWorkerOptions: pdfjs.GlobalWorkerOptions,
	};
};

export interface IPdfService {
	getPdfDocumentProxy(options: { id: string; buffer: ArrayBuffer }): Promise<PDFDocumentProxy>;
	clearCache(): void;
	removeFromCache(id: string): void;
	getCacheSize(): number;
}

export interface IPdfLoaderConfig {
	workerSrc: string;
	cMapUrl: string;
	cMapPacked: boolean;
	maxCacheSize?: number;
}

class PdfCache {
	private cache = new Map<string, PDFDocumentProxy>();
	private accessOrder = new Map<string, number>();
	private accessCounter = 0;
	private maxSize: number;

	constructor(maxSize = 50) {
		this.maxSize = maxSize;
	}

	get(id: string): PDFDocumentProxy | undefined {
		const item = this.cache.get(id);
		if (item) {
			this.accessOrder.set(id, ++this.accessCounter);
		}
		return item;
	}

	set(id: string, pdf: PDFDocumentProxy): void {
		if (this.cache.size >= this.maxSize && !this.cache.has(id)) {
			this.evictLeastRecentlyUsed();
		}

		this.cache.set(id, pdf);
		this.accessOrder.set(id, ++this.accessCounter);
	}

	has(id: string): boolean {
		return this.cache.has(id);
	}

	remove(id: string): boolean {
		this.accessOrder.delete(id);
		return this.cache.delete(id);
	}

	clear(): void {
		this.cache.clear();
		this.accessOrder.clear();
		this.accessCounter = 0;
	}

	size(): number {
		return this.cache.size;
	}

	private evictLeastRecentlyUsed(): void {
		let lruId = "";
		let lruAccess = Infinity;

		for (const [id, accessTime] of this.accessOrder) {
			if (accessTime < lruAccess) {
				lruAccess = accessTime;
				lruId = id;
			}
		}

		if (lruId) {
			this.remove(lruId);
		}
	}
}

export class PdfService implements IPdfService {
	private cache: PdfCache;
	private loaderConfig: IPdfLoaderConfig;
	private loadingTasks = new Map<string, Promise<PDFDocumentProxy>>();
	private pdfJsModule: PdfJsModule | null = null;

	constructor(loaderConfig?: Partial<IPdfLoaderConfig>) {
		this.loaderConfig = {
			...PDF_WORKER_SETTINGS,
			...loaderConfig,
		};

		this.cache = new PdfCache(this.loaderConfig.maxCacheSize);
	}

	private async ensurePdfJsLoaded(): Promise<PdfJsModule> {
		if (!this.pdfJsModule) {
			this.pdfJsModule = await loadPdfJs();
			this.pdfJsModule.GlobalWorkerOptions.workerSrc = this.loaderConfig.workerSrc;
		}
		return this.pdfJsModule;
	}

	public async getPdfDocumentProxy({ id, buffer }: { id: string; buffer: ArrayBuffer }): Promise<PDFDocumentProxy> {
		if (!buffer || buffer.byteLength === 0) {
			throw new Error("Buffer de PDF vazio ou inválido");
		}

		if (!id?.trim()) {
			throw new Error("ID do documento é obrigatório");
		}

		if (this.cache.has(id)) {
			return this.cache.get(id)!;
		}

		if (this.loadingTasks.has(id)) {
			return this.loadingTasks.get(id)!;
		}

		const loadingPromise = this.loadPdfDocument(id, buffer);
		this.loadingTasks.set(id, loadingPromise);

		try {
			const pdf = await loadingPromise;
			return pdf;
		} finally {
			this.loadingTasks.delete(id);
		}
	}

	private async loadPdfDocument(id: string, buffer: ArrayBuffer): Promise<PDFDocumentProxy> {
		try {
			const pdfJs = await this.ensurePdfJsLoaded();

			const clonedBuffer = buffer.slice(0);
			const loadingTask: PDFDocumentLoadingTask = pdfJs.getDocument({
				data: clonedBuffer,
				disableFontFace: false,
				cMapUrl: this.loaderConfig.cMapUrl,
				cMapPacked: this.loaderConfig.cMapPacked,
				verbosity: 0,
			});

			const pdf: PDFDocumentProxy = await loadingTask.promise;
			this.cache.set(id, pdf);

			return pdf;
		} catch (error) {
			const errorMessage = `Falha ao carregar o documento PDF para o id: ${id}`;
			console.error(errorMessage, error);
			throw new Error(errorMessage);
		}
	}

	public removeFromCache(id: string): void {
		this.cache.remove(id);
	}

	public clearCache(): void {
		this.cache.clear();
		this.loadingTasks.clear();
	}

	public getCacheSize(): number {
		return this.cache.size();
	}
}
